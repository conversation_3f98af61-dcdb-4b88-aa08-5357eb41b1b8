import 'dart:io';
import 'dart:convert';
import 'dart:typed_data';
import 'package:archive/archive.dart';
import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart' as html_dom;
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:flutter/material.dart';

/// Production-grade cross-platform EPUB metadata extraction service
///
/// Provides comprehensive native EPUB parsing capabilities for all platforms
/// as a primary extraction method, with WebView-based extraction as fallback.
/// Supports both EPUB 2.0 and EPUB 3.0 standards with proper error handling
/// and graceful degradation. Follows Flutter best practices for unified
/// cross-platform functionality.
class EpubMetadataExtractor {
  /// Extracted metadata from EPUB file
  static const String _containerPath = 'META-INF/container.xml';

  /// Extract comprehensive metadata from EPUB file
  ///
  /// Returns [EpubMetadata] with extracted information or throws [EpubExtractionException]
  /// on failure. Includes proper error handling and logging for production use.
  static Future<EpubMetadata> extractMetadata(File epubFile) async {
    try {
      AnxLog.info(
        'Starting production EPUB metadata extraction: ${epubFile.path}',
      );

      // Verify file exists and is readable
      if (!await epubFile.exists()) {
        throw EpubExtractionException(
          'EPUB file does not exist: ${epubFile.path}',
          EpubErrorType.fileNotFound,
        );
      }

      final fileSize = await epubFile.length();
      if (fileSize == 0) {
        throw EpubExtractionException(
          'EPUB file is empty: ${epubFile.path}',
          EpubErrorType.invalidFile,
        );
      }

      AnxLog.info('EPUB file size: $fileSize bytes');

      // Read EPUB as ZIP archive
      final bytes = await epubFile.readAsBytes();
      final archive = ZipDecoder().decodeBytes(bytes);

      AnxLog.info('EPUB archive contains ${archive.files.length} files');

      // Extract container.xml to find OPF file
      final opfPath = await _extractOpfPath(archive);
      AnxLog.info('Found OPF file path: $opfPath');

      // Extract and parse OPF file for metadata
      final metadata = await _extractMetadataFromOpf(archive, opfPath);

      // Extract cover image if available
      final coverData =
          await _extractCoverImage(archive, opfPath, metadata.coverHref);
      if (coverData != null) {
        metadata.coverData = coverData;
        AnxLog.info('Extracted cover image: ${coverData.length} bytes');
      }

      AnxLog.info('EPUB metadata extraction completed successfully');
      AnxLog.info('Title: ${metadata.title}, Author: ${metadata.author}');

      return metadata;
    } catch (e) {
      AnxLog.severe('EPUB metadata extraction failed: $e');

      if (e is EpubExtractionException) {
        rethrow;
      }

      // Wrap unexpected errors
      throw EpubExtractionException(
        'Unexpected error during EPUB metadata extraction: $e',
        EpubErrorType.parsingError,
        originalError: e,
      );
    }
  }

  /// Extract OPF file path from container.xml
  static Future<String> _extractOpfPath(Archive archive) async {
    final containerFile = archive.files.firstWhere(
      (file) => file.name == _containerPath,
      orElse: () => throw const EpubExtractionException(
        'Container file not found: $_containerPath',
        EpubErrorType.invalidEpubStructure,
      ),
    );

    // Archive files always have content, but check for empty content
    final content = containerFile.content as List<int>?;
    if (content == null || content.isEmpty) {
      throw const EpubExtractionException(
        'Container file is empty',
        EpubErrorType.invalidEpubStructure,
      );
    }

    final containerXml = utf8.decode(content);
    final document = html_parser.parse(containerXml);

    // Find rootfile element with OPF media type
    final rootfileElements = document.querySelectorAll('rootfile');
    for (final element in rootfileElements) {
      final mediaType = element.attributes['media-type'];
      if (mediaType == 'application/oebps-package+xml') {
        final fullPath = element.attributes['full-path'];
        if (fullPath != null && fullPath.isNotEmpty) {
          return fullPath;
        }
      }
    }

    throw const EpubExtractionException(
      'No valid OPF file found in container.xml',
      EpubErrorType.invalidEpubStructure,
    );
  }

  /// Extract metadata from OPF file
  static Future<EpubMetadata> _extractMetadataFromOpf(
    Archive archive,
    String opfPath,
  ) async {
    final opfFile = archive.files.firstWhere(
      (file) => file.name == opfPath,
      orElse: () => throw EpubExtractionException(
        'OPF file not found: $opfPath',
        EpubErrorType.invalidEpubStructure,
      ),
    );

    // Archive files always have content, but check for empty content
    final opfContent = opfFile.content as List<int>?;
    if (opfContent == null || opfContent.isEmpty) {
      throw const EpubExtractionException(
        'OPF file is empty',
        EpubErrorType.invalidEpubStructure,
      );
    }

    final opfXml = utf8.decode(opfContent);
    final document = html_parser.parse(opfXml);

    // Extract basic metadata
    final title = _extractTextContent(document, 'dc\\:title') ??
        _extractTextContent(document, 'title') ??
        'Unknown Title';

    final author = _extractTextContent(document, 'dc\\:creator') ??
        _extractTextContent(document, 'creator') ??
        'Unknown Author';

    final description = _extractTextContent(document, 'dc\\:description') ??
        _extractTextContent(document, 'description') ??
        '';

    final language = _extractTextContent(document, 'dc\\:language') ??
        _extractTextContent(document, 'language') ??
        'en';

    final publisher = _extractTextContent(document, 'dc\\:publisher') ??
        _extractTextContent(document, 'publisher') ??
        '';

    // Extract cover reference
    String? coverHref = _extractCoverHref(document, opfPath);

    return EpubMetadata(
      title: title.trim(),
      author: author.trim(),
      description: description.trim(),
      language: language.trim(),
      publisher: publisher.trim(),
      coverHref: coverHref,
    );
  }

  /// Extract text content from XML element
  static String? _extractTextContent(
    html_dom.Document document,
    String selector,
  ) {
    try {
      final element = document.querySelector(selector);
      return element?.text.trim();
    } catch (e) {
      // Try alternative selectors for different EPUB versions
      final alternativeSelectors = [
        selector.replaceAll('dc\\:', ''),
        selector.replaceAll('\\:', ':'),
      ];

      for (final altSelector in alternativeSelectors) {
        try {
          final element = document.querySelector(altSelector);
          final text = element?.text.trim();
          if (text != null && text.isNotEmpty) {
            return text;
          }
        } catch (_) {
          // Continue to next selector
        }
      }
    }
    return null;
  }

  /// Extract cover image reference from OPF
  static String? _extractCoverHref(html_dom.Document document, String opfPath) {
    try {
      // Method 1: Look for cover-image property (EPUB 3)
      final manifestItems = document.querySelectorAll('item');
      for (final item in manifestItems) {
        final properties = item.attributes['properties'];
        if (properties != null && properties.contains('cover-image')) {
          return _resolveHref(item.attributes['href'], opfPath);
        }
      }

      // Method 2: Look for meta name="cover" (EPUB 2)
      final metaElements = document.querySelectorAll('meta');
      for (final meta in metaElements) {
        if (meta.attributes['name'] == 'cover') {
          final coverId = meta.attributes['content'];
          if (coverId != null) {
            final coverItem = manifestItems.firstWhere(
              (item) => item.attributes['id'] == coverId,
              orElse: () => throw StateError('Cover item not found'),
            );
            return _resolveHref(coverItem.attributes['href'], opfPath);
          }
        }
      }

      // Method 3: Look for guide reference type="cover"
      final guideRefs = document.querySelectorAll('reference');
      for (final ref in guideRefs) {
        final type = ref.attributes['type'];
        if (type != null && type.contains('cover')) {
          return _resolveHref(ref.attributes['href'], opfPath);
        }
      }
    } catch (e) {
      AnxLog.warning('Failed to extract cover reference: $e');
    }

    return null;
  }

  /// Resolve relative href against OPF path
  static String? _resolveHref(String? href, String opfPath) {
    if (href == null || href.isEmpty) return null;

    if (href.startsWith('/')) {
      return href.substring(1); // Remove leading slash
    }

    final opfDir = opfPath.contains('/')
        ? opfPath.substring(0, opfPath.lastIndexOf('/'))
        : '';

    return opfDir.isEmpty ? href : '$opfDir/$href';
  }

  /// Extract cover image data
  static Future<Uint8List?> _extractCoverImage(
    Archive archive,
    String opfPath,
    String? coverHref,
  ) async {
    if (coverHref == null) return null;

    try {
      final coverFile = archive.files.firstWhere(
        (file) => file.name == coverHref,
        orElse: () => throw StateError('Cover file not found: $coverHref'),
      );

      // Archive files always have content
      return Uint8List.fromList(coverFile.content as List<int>);
    } catch (e) {
      AnxLog.warning('Failed to extract cover image: $e');
    }

    return null;
  }
}

/// EPUB metadata container
class EpubMetadata {
  final String title;
  final String author;
  final String description;
  final String language;
  final String publisher;
  final String? coverHref;
  Uint8List? coverData;

  EpubMetadata({
    required this.title,
    required this.author,
    required this.description,
    required this.language,
    required this.publisher,
    this.coverHref,
    this.coverData,
  });

  /// Convert cover data to base64 string for storage
  String get coverBase64 {
    if (coverData == null) return '';
    return 'data:image/jpeg;base64,${base64Encode(coverData!)}';
  }

  @override
  String toString() {
    return 'EpubMetadata(title: $title, author: $author, description: ${description.length} chars, coverData: ${coverData?.length ?? 0} bytes)';
  }
}

/// EPUB extraction exception with detailed error information
class EpubExtractionException implements Exception {
  final String message;
  final EpubErrorType errorType;
  final Object? originalError;

  const EpubExtractionException(
    this.message,
    this.errorType, {
    this.originalError,
  });

  /// Get localized error message for user display
  String getLocalizedMessage(BuildContext context) {
    final l10n = L10n.of(context);

    switch (errorType) {
      case EpubErrorType.fileNotFound:
        return l10n.error_file_not_found;
      case EpubErrorType.invalidFile:
        return l10n.error_invalid_file;
      case EpubErrorType.invalidEpubStructure:
        return l10n.error_invalid_epub;
      case EpubErrorType.parsingError:
        return l10n.error_parsing_failed;
      case EpubErrorType.unsupportedFormat:
        return l10n.error_unsupported_format;
    }
  }

  @override
  String toString() {
    return 'EpubExtractionException: $message (type: $errorType)${originalError != null ? ', caused by: $originalError' : ''}';
  }
}

/// Types of EPUB extraction errors
enum EpubErrorType {
  fileNotFound,
  invalidFile,
  invalidEpubStructure,
  parsingError,
  unsupportedFormat,
}
